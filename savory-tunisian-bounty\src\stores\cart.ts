import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import type { Database } from "@/lib/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

export interface CartItem {
  product: Product;
  quantity: number;
}

export const useCartStore = defineStore("cart", () => {
  const items = ref<CartItem[]>([]);

  // Computed properties
  const itemCount = computed(() =>
    items.value.reduce((total, item) => total + item.quantity, 0)
  );

  const totalAmount = computed(() =>
    items.value.reduce(
      (total, item) => total + item.product.price * item.quantity,
      0
    )
  );

  const isEmpty = computed(() => items.value.length === 0);

  // Actions
  const addItem = (product: Product, quantity: number = 1) => {
    const existingItem = items.value.find(
      (item) => item.product.id === product.id
    );

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      items.value.push({ product, quantity });
    }

    saveToLocalStorage();
  };

  const removeItem = (productId: string) => {
    const index = items.value.findIndex(
      (item) => item.product.id === productId
    );
    if (index > -1) {
      items.value.splice(index, 1);
      saveToLocalStorage();
    }
  };

  const updateQuantity = (productId: string, quantity: number) => {
    const item = items.value.find((item) => item.product.id === productId);
    if (item) {
      if (quantity <= 0) {
        removeItem(productId);
      } else {
        item.quantity = quantity;
        saveToLocalStorage();
      }
    }
  };

  const clearCart = () => {
    items.value = [];
    saveToLocalStorage();
  };

  const getItemQuantity = (productId: string): number => {
    const item = items.value.find((item) => item.product.id === productId);
    return item ? item.quantity : 0;
  };

  const isInCart = (productId: string): boolean => {
    return items.value.some((item) => item.product.id === productId);
  };

  // Persistence
  const saveToLocalStorage = () => {
    try {
      localStorage.setItem("cart", JSON.stringify(items.value));
    } catch (error) {
      console.error("Error saving cart to localStorage:", error);
    }
  };

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem("cart");
      if (saved) {
        items.value = JSON.parse(saved);
      }
    } catch (error) {
      console.error("Error loading cart from localStorage:", error);
      items.value = [];
    }
  };

  // Initialize cart from localStorage
  const initialize = () => {
    loadFromLocalStorage();
  };

  return {
    items: readonly(items),
    itemCount,
    totalAmount,
    isEmpty,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getItemQuantity,
    isInCart,
    initialize,
  };
});
