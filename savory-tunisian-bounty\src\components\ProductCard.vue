<template>
  <div class="card hover:shadow-lg transition-shadow duration-300">
    <div class="relative">
      <img 
        :src="product.image_url || '/placeholder-product.jpg'" 
        :alt="product.name"
        class="w-full h-48 object-cover"
        @error="handleImageError"
      />
      <div class="absolute top-2 right-2">
        <span 
          v-if="product.stock_quantity <= 5" 
          class="bg-red-500 text-white text-xs px-2 py-1 rounded"
        >
          Low Stock
        </span>
      </div>
    </div>
    
    <div class="p-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
        {{ product.name }}
      </h3>
      
      <p class="text-gray-600 text-sm mb-3 line-clamp-2">
        {{ product.description }}
      </p>
      
      <div class="flex items-center justify-between mb-4">
        <span class="text-2xl font-bold text-primary-600">
          ${{ product.price.toFixed(2) }}
        </span>
        <span class="text-sm text-gray-500">
          {{ product.stock_quantity }} in stock
        </span>
      </div>
      
      <div class="flex space-x-2">
        <button 
          @click="viewProduct"
          class="flex-1 btn-outline text-sm py-2"
        >
          View Details
        </button>
        
        <button 
          @click="addToCart"
          :disabled="product.stock_quantity === 0"
          class="flex-1 btn-primary text-sm py-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isInCart">In Cart ({{ cartQuantity }})</span>
          <span v-else>Add to Cart</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import type { Database } from '@/lib/supabase'

type Product = Database['public']['Tables']['products']['Row']

interface Props {
  product: Product
}

const props = defineProps<Props>()
const router = useRouter()
const cartStore = useCartStore()

const isInCart = computed(() => cartStore.isInCart(props.product.id))
const cartQuantity = computed(() => cartStore.getItemQuantity(props.product.id))

const viewProduct = () => {
  router.push({ name: 'product-detail', params: { id: props.product.id } })
}

const addToCart = () => {
  if (props.product.stock_quantity > 0) {
    cartStore.addItem(props.product, 1)
  }
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400'
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
