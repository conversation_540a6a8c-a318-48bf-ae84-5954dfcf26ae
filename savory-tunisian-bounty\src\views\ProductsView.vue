<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-serif font-bold text-gray-900 mb-4">
          Our Products
        </h1>
        <p class="text-lg text-gray-600">
          Discover authentic Tunisian flavors and traditional specialties
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="flex flex-col lg:flex-row gap-4">
          <!-- Enhanced Search -->
          <div class="flex-1">
            <SearchBar
              v-model="searchQuery"
              @search="handleSearch"
              placeholder="Search products, categories, or keywords..."
            />
          </div>

          <!-- Category Filter -->
          <div class="lg:w-64">
            <select
              v-model="selectedCategory"
              class="input-field"
              @change="handleCategoryFilter"
            >
              <option value="">All Categories</option>
              <option
                v-for="category in categories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- Advanced Filters Toggle -->
          <button
            @click="showAdvancedFilters = !showAdvancedFilters"
            class="btn-outline whitespace-nowrap flex items-center space-x-2"
          >
            <svg
              class="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
              />
            </svg>
            <span>Filters</span>
          </button>

          <!-- Clear Filters -->
          <button
            v-if="searchQuery || selectedCategory || hasAdvancedFilters"
            @click="clearFilters"
            class="btn-outline whitespace-nowrap"
          >
            Clear All
          </button>
        </div>

        <!-- Advanced Filters Panel -->
        <div
          v-if="showAdvancedFilters"
          class="mt-6 pt-6 border-t border-gray-200"
        >
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Price Range -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Price Range</label
              >
              <div class="flex items-center space-x-2">
                <input
                  v-model.number="priceRange.min"
                  type="number"
                  placeholder="Min"
                  class="input-field text-sm"
                  min="0"
                />
                <span class="text-gray-500">-</span>
                <input
                  v-model.number="priceRange.max"
                  type="number"
                  placeholder="Max"
                  class="input-field text-sm"
                  min="0"
                />
              </div>
            </div>

            <!-- Sort Options -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Sort By</label
              >
              <select
                v-model="sortBy"
                @change="applySorting"
                class="input-field text-sm"
              >
                <option value="name">Name (A-Z)</option>
                <option value="name-desc">Name (Z-A)</option>
                <option value="price">Price (Low to High)</option>
                <option value="price-desc">Price (High to Low)</option>
                <option value="newest">Newest First</option>
              </select>
            </div>

            <!-- Stock Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2"
                >Availability</label
              >
              <select v-model="stockFilter" class="input-field text-sm">
                <option value="">All Products</option>
                <option value="in-stock">In Stock Only</option>
                <option value="low-stock">Low Stock</option>
              </select>
            </div>
          </div>

          <div class="mt-4 flex justify-end">
            <button @click="applyAdvancedFilters" class="btn-primary">
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Results Info -->
      <div class="flex justify-between items-center mb-6">
        <p class="text-gray-600">
          {{ filteredProducts.length }} product{{
            filteredProducts.length !== 1 ? "s" : ""
          }}
          found
        </p>

        <div class="flex items-center space-x-4">
          <!-- Sort (placeholder for future implementation) -->
          <select class="input-field w-auto">
            <option>Sort by Name</option>
            <option>Sort by Price</option>
            <option>Sort by Newest</option>
          </select>
        </div>
      </div>

      <!-- Products Grid -->
      <div
        v-if="!loading && filteredProducts.length > 0"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <ProductCard
          v-for="product in filteredProducts"
          :key="product.id"
          :product="product"
        />
      </div>

      <!-- Loading State -->
      <div
        v-else-if="loading"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <div v-for="i in 8" :key="i" class="card animate-pulse">
          <div class="h-48 bg-gray-200"></div>
          <div class="p-4">
            <div class="h-6 bg-gray-200 rounded mb-2"></div>
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-6 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">
          No products found
        </h3>
        <p class="text-gray-600 mb-6">
          Try adjusting your search criteria or browse our categories
        </p>
        <button @click="clearFilters" class="btn-primary">Clear Filters</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useProductsStore } from "@/stores/products";
import ProductCard from "@/components/ProductCard.vue";
import SearchBar from "@/components/SearchBar.vue";

const route = useRoute();
const router = useRouter();
const productsStore = useProductsStore();

const searchQuery = ref("");
const selectedCategory = ref("");
const loading = ref(true);

// Advanced filters
const showAdvancedFilters = ref(false);
const priceRange = ref({
  min: null as number | null,
  max: null as number | null,
});
const sortBy = ref("name");
const stockFilter = ref("");

const categories = computed(() => productsStore.categories);

// Enhanced filtered products with advanced filters
const filteredProducts = computed(() => {
  let products = productsStore.filteredProducts;

  // Apply price range filter
  if (priceRange.value.min !== null || priceRange.value.max !== null) {
    products = products.filter((product) => {
      const price = product.price;
      const minOk =
        priceRange.value.min === null || price >= priceRange.value.min;
      const maxOk =
        priceRange.value.max === null || price <= priceRange.value.max;
      return minOk && maxOk;
    });
  }

  // Apply stock filter
  if (stockFilter.value) {
    products = products.filter((product) => {
      switch (stockFilter.value) {
        case "in-stock":
          return product.stock_quantity > 0;
        case "low-stock":
          return product.stock_quantity > 0 && product.stock_quantity <= 5;
        default:
          return true;
      }
    });
  }

  // Apply sorting
  products = [...products].sort((a, b) => {
    switch (sortBy.value) {
      case "name":
        return a.name.localeCompare(b.name);
      case "name-desc":
        return b.name.localeCompare(a.name);
      case "price":
        return a.price - b.price;
      case "price-desc":
        return b.price - a.price;
      case "newest":
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      default:
        return 0;
    }
  });

  return products;
});

const hasAdvancedFilters = computed(() => {
  return (
    priceRange.value.min !== null ||
    priceRange.value.max !== null ||
    stockFilter.value !== "" ||
    sortBy.value !== "name"
  );
});

const handleSearch = () => {
  productsStore.searchProducts(searchQuery.value);
  updateURL();
};

const handleCategoryFilter = () => {
  productsStore.filterByCategory(selectedCategory.value || null);
  updateURL();
};

const clearFilters = () => {
  searchQuery.value = "";
  selectedCategory.value = "";
  priceRange.value = { min: null, max: null };
  sortBy.value = "name";
  stockFilter.value = "";
  showAdvancedFilters.value = false;
  productsStore.clearFilters();
  router.push({ name: "products" });
};

const applySorting = () => {
  // Sorting is handled in the computed property
  updateURL();
};

const applyAdvancedFilters = () => {
  updateURL();
};

const updateURL = () => {
  const query: Record<string, string> = {};

  if (searchQuery.value) {
    query.search = searchQuery.value;
  }

  if (selectedCategory.value) {
    query.category = selectedCategory.value;
  }

  router.push({ name: "products", query });
};

// Initialize from URL parameters
const initializeFromURL = () => {
  if (route.query.search) {
    searchQuery.value = route.query.search as string;
  }

  if (route.query.category) {
    selectedCategory.value = route.query.category as string;
  }
};

onMounted(async () => {
  try {
    await productsStore.initialize();
    initializeFromURL();

    // Apply filters if they exist
    if (searchQuery.value) {
      productsStore.searchProducts(searchQuery.value);
    }

    if (selectedCategory.value) {
      productsStore.filterByCategory(selectedCategory.value);
    }
  } catch (error) {
    console.error("Error loading products:", error);
  } finally {
    loading.value = false;
  }
});

// Watch for route changes
watch(
  () => route.query,
  () => {
    initializeFromURL();
  },
  { deep: true }
);
</script>
