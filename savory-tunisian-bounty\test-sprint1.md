# Sprint 1 Testing Checklist

## User Stories to Test

### US001: As a new customer, I want to create an account with my email and password, so that I can securely log in.
- [ ] Navigate to /register
- [ ] Fill out registration form with valid data
- [ ] Submit form and verify account creation
- [ ] Check for email confirmation (if required)
- [ ] Verify user is redirected appropriately

### US002: As an existing customer, I want to log in using my email and password, so that I can access my account.
- [ ] Navigate to /login
- [ ] Enter valid credentials
- [ ] Submit form and verify successful login
- [ ] Check that user session is maintained
- [ ] Verify user menu appears in header

### US006: As a customer, I want to log out of my account, so that my session is securely ended.
- [ ] Click logout button in user menu
- [ ] Verify user is logged out
- [ ] Check that protected routes redirect to login
- [ ] Verify user menu is replaced with login/register buttons

### US007: As a visitor, I want to browse products by category, so that I can easily find specific types of items.
- [ ] Navigate to /products
- [ ] Verify products are displayed
- [ ] Test category filtering
- [ ] Test search functionality
- [ ] Verify product cards display correctly

## Additional Core Functionality Tests

### Navigation and Routing
- [ ] Home page loads correctly
- [ ] Products page loads and displays products
- [ ] Product detail page works with valid product ID
- [ ] Cart page displays correctly
- [ ] Authentication routes work properly

### Product Browsing
- [ ] Products load from Supabase
- [ ] Categories load and display
- [ ] Search functionality works
- [ ] Category filtering works
- [ ] Product images display (with fallback)

### Shopping Cart
- [ ] Add products to cart
- [ ] Update quantities in cart
- [ ] Remove items from cart
- [ ] Cart persists in localStorage
- [ ] Cart counter updates in header

### UI/UX
- [ ] Responsive design works on mobile
- [ ] Loading states display correctly
- [ ] Error states handle gracefully
- [ ] Tailwind CSS styles apply correctly
- [ ] Navigation is intuitive

## Test Results

### Passed ✅
- 

### Failed ❌
- 

### Notes
- 
