<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-serif font-bold text-gray-900 mb-8">My Profile</h1>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="space-y-6">
          <div>
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Profile Information</h2>
            
            <form @submit.prevent="updateProfile" class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    id="fullName"
                    v-model="form.fullName"
                    type="text"
                    class="input-field"
                  />
                </div>
                
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    id="email"
                    v-model="form.email"
                    type="email"
                    disabled
                    class="input-field bg-gray-50"
                  />
                </div>
                
                <div>
                  <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    id="phone"
                    v-model="form.phone"
                    type="tel"
                    class="input-field"
                  />
                </div>
              </div>
              
              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="updating"
                  class="btn-primary disabled:opacity-50"
                >
                  <span v-if="updating">Updating...</span>
                  <span v-else>Update Profile</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
const updating = ref(false)

const form = reactive({
  fullName: '',
  email: '',
  phone: ''
})

const updateProfile = async () => {
  try {
    updating.value = true
    await authStore.updateProfile({
      full_name: form.fullName,
      phone: form.phone
    })
  } catch (error) {
    console.error('Error updating profile:', error)
  } finally {
    updating.value = false
  }
}

onMounted(() => {
  if (authStore.profile) {
    form.fullName = authStore.profile.full_name || ''
    form.email = authStore.profile.email
    form.phone = authStore.profile.phone || ''
  }
})
</script>
