<template>
  <div class="min-h-screen bg-gray-50">
    <div v-if="loading" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="animate-pulse">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="h-96 bg-gray-200 rounded-lg"></div>
          <div class="space-y-4">
            <div class="h-8 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-6 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-else-if="product"
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
    >
      <!-- Breadcrumb -->
      <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-gray-500">
          <li>
            <router-link to="/" class="hover:text-primary-600"
              >Home</router-link
            >
          </li>
          <li>/</li>
          <li>
            <router-link to="/products" class="hover:text-primary-600"
              >Products</router-link
            >
          </li>
          <li>/</li>
          <li class="text-gray-900">{{ product.name }}</li>
        </ol>
      </nav>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Product Images -->
        <div>
          <!-- Main Image -->
          <div
            class="aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4 relative group"
          >
            <img
              :src="currentImage"
              :alt="product.name"
              class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              @error="handleImageError"
            />

            <!-- Image Navigation Arrows -->
            <div
              v-if="allImages.length > 1"
              class="absolute inset-0 flex items-center justify-between p-4 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <button
                @click="previousImage"
                class="bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-colors"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <button
                @click="nextImage"
                class="bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-colors"
              >
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            <!-- Image Counter -->
            <div
              v-if="allImages.length > 1"
              class="absolute bottom-4 right-4 bg-black/50 text-white text-sm px-2 py-1 rounded"
            >
              {{ currentImageIndex + 1 }} / {{ allImages.length }}
            </div>
          </div>

          <!-- Image Thumbnails -->
          <div v-if="allImages.length > 1" class="grid grid-cols-4 gap-2">
            <div
              v-for="(image, index) in allImages.slice(0, 4)"
              :key="index"
              @click="setCurrentImage(index)"
              :class="[
                'aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-all duration-200',
                currentImageIndex === index
                  ? 'ring-2 ring-primary-500 ring-offset-2'
                  : 'hover:opacity-75 hover:ring-1 hover:ring-gray-300',
              ]"
            >
              <img
                :src="image"
                :alt="`${product.name} ${index + 1}`"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
            </div>

            <!-- Show more images indicator -->
            <div
              v-if="allImages.length > 4"
              class="aspect-square bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
            >
              <div class="text-center">
                <svg
                  class="w-6 h-6 mx-auto mb-1 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <span class="text-xs text-gray-500"
                  >+{{ allImages.length - 4 }}</span
                >
              </div>
            </div>
          </div>
        </div>

        <!-- Product Info -->
        <div>
          <h1 class="text-3xl font-serif font-bold text-gray-900 mb-4">
            {{ product.name }}
          </h1>

          <div class="flex items-center space-x-4 mb-6">
            <span class="text-3xl font-bold text-primary-600">
              ${{ product.price.toFixed(2) }}
            </span>
            <span
              v-if="product.stock_quantity <= 5"
              class="bg-red-100 text-red-800 text-sm px-2 py-1 rounded"
            >
              Only {{ product.stock_quantity }} left!
            </span>
            <span
              v-else-if="product.stock_quantity > 0"
              class="bg-green-100 text-green-800 text-sm px-2 py-1 rounded"
            >
              In Stock
            </span>
            <span
              v-else
              class="bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded"
            >
              Out of Stock
            </span>
          </div>

          <div class="prose prose-gray max-w-none mb-8">
            <p>{{ product.description }}</p>
          </div>

          <!-- Quantity and Add to Cart -->
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <div class="flex items-center space-x-3">
                <button
                  @click="quantity = Math.max(1, quantity - 1)"
                  class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M20 12H4"
                    />
                  </svg>
                </button>

                <span class="w-16 text-center font-medium text-lg">{{
                  quantity
                }}</span>

                <button
                  @click="
                    quantity = Math.min(product.stock_quantity, quantity + 1)
                  "
                  :disabled="quantity >= product.stock_quantity"
                  class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg
                    class="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <div class="flex space-x-4">
              <button
                @click="addToCart"
                :disabled="product.stock_quantity === 0 || addingToCart"
                class="flex-1 btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="addingToCart">Adding...</span>
                <span v-else-if="isInCart">Add More to Cart</span>
                <span v-else>Add to Cart</span>
              </button>

              <button class="btn-outline px-6 py-3">
                <svg
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </button>
            </div>

            <div
              v-if="isInCart"
              class="bg-green-50 border border-green-200 rounded-lg p-4"
            >
              <div class="flex items-center">
                <svg
                  class="w-5 h-5 text-green-400 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="text-green-800">
                  {{ cartQuantity }} item{{ cartQuantity !== 1 ? "s" : "" }} in
                  cart
                </span>
              </div>
            </div>
          </div>

          <!-- Product Details -->
          <div class="mt-8 border-t border-gray-200 pt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Product Details
            </h3>
            <dl class="space-y-3">
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">SKU:</dt>
                <dd class="text-sm text-gray-900">
                  {{ product.id.slice(0, 8).toUpperCase() }}
                </dd>
              </div>
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">Stock:</dt>
                <dd class="text-sm text-gray-900">
                  {{ product.stock_quantity }} units
                </dd>
              </div>
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">
                  Category:
                </dt>
                <dd class="text-sm text-gray-900">{{ categoryName }}</dd>
              </div>
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">Weight:</dt>
                <dd class="text-sm text-gray-900">
                  {{ (product as any).weight || "N/A" }}
                </dd>
              </div>
              <div class="flex">
                <dt class="w-1/3 text-sm font-medium text-gray-500">Origin:</dt>
                <dd class="text-sm text-gray-900">
                  {{ (product as any).origin || "Tunisia" }}
                </dd>
              </div>
            </dl>
          </div>

          <!-- Product Features -->
          <div class="mt-8 border-t border-gray-200 pt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Features & Benefits
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <svg
                    class="w-5 h-5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <span class="text-sm text-gray-700"
                  >100% Authentic Tunisian</span
                >
              </div>
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <svg
                    class="w-5 h-5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <span class="text-sm text-gray-700">Premium Quality</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <svg
                    class="w-5 h-5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <span class="text-sm text-gray-700">Natural & Organic</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <svg
                    class="w-5 h-5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <span class="text-sm text-gray-700">Fast Shipping</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Products Section -->
      <div
        v-if="relatedProducts.length > 0"
        class="mt-16 border-t border-gray-200 pt-16"
      >
        <h2 class="text-2xl font-serif font-bold text-gray-900 mb-8">
          You Might Also Like
        </h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <ProductCard
            v-for="relatedProduct in relatedProducts.slice(0, 4)"
            :key="relatedProduct.id"
            :product="relatedProduct"
          />
        </div>
      </div>
    </div>

    <!-- Product not found -->
    <div
      v-else
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center"
    >
      <div class="text-6xl mb-4">🔍</div>
      <h1 class="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
      <p class="text-gray-600 mb-8">
        The product you're looking for doesn't exist or has been removed.
      </p>
      <router-link to="/products" class="btn-primary">
        Browse Products
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useProductsStore } from "@/stores/products";
import { useCartStore } from "@/stores/cart";
import ProductCard from "@/components/ProductCard.vue";
import type { Database } from "@/lib/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

interface Props {
  id: string;
}

const props = defineProps<Props>();
const route = useRoute();
const productsStore = useProductsStore();
const cartStore = useCartStore();

const product = ref<Product | null>(null);
const loading = ref(true);
const quantity = ref(1);
const addingToCart = ref(false);
const currentImageIndex = ref(0);

const isInCart = computed(() =>
  product.value ? cartStore.isInCart(product.value.id) : false
);
const cartQuantity = computed(() =>
  product.value ? cartStore.getItemQuantity(product.value.id) : 0
);

const categoryName = computed(() => {
  if (!product.value?.category_id) return "Uncategorized";
  const category = productsStore.categories.find(
    (c) => c.id === product.value?.category_id
  );
  return category?.name || "Uncategorized";
});

// Image gallery computed properties
const allImages = computed(() => {
  if (!product.value) return [];
  const images = [];

  // Add main image first
  if (product.value.image_url) {
    images.push(product.value.image_url);
  }

  // Add additional images
  if (product.value.images && Array.isArray(product.value.images)) {
    images.push(...product.value.images);
  }

  // If no images, return placeholder
  if (images.length === 0) {
    images.push(
      "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400"
    );
  }

  return images;
});

const currentImage = computed(() => {
  return allImages.value[currentImageIndex.value] || allImages.value[0];
});

// Related products
const relatedProducts = computed(() => {
  if (!product.value || !product.value.category_id) return [];

  return productsStore.products
    .filter(
      (p) =>
        p.category_id === product.value?.category_id &&
        p.id !== product.value?.id &&
        p.is_active
    )
    .slice(0, 4);
});

const addToCart = async () => {
  if (!product.value || product.value.stock_quantity === 0) return;

  try {
    addingToCart.value = true;
    cartStore.addItem(product.value, quantity.value);

    // Reset quantity after adding
    quantity.value = 1;
  } catch (error) {
    console.error("Error adding to cart:", error);
  } finally {
    addingToCart.value = false;
  }
};

// Image navigation methods
const setCurrentImage = (index: number) => {
  currentImageIndex.value = index;
};

const nextImage = () => {
  currentImageIndex.value =
    (currentImageIndex.value + 1) % allImages.value.length;
};

const previousImage = () => {
  currentImageIndex.value =
    currentImageIndex.value === 0
      ? allImages.value.length - 1
      : currentImageIndex.value - 1;
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src =
    "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400";
};

onMounted(async () => {
  try {
    const productId = props.id || (route.params.id as string);

    // Ensure categories are loaded for category name display
    if (productsStore.categories.length === 0) {
      await productsStore.fetchCategories();
    }

    product.value = await productsStore.getProductById(productId);
  } catch (error) {
    console.error("Error loading product:", error);
  } finally {
    loading.value = false;
  }
});
</script>
