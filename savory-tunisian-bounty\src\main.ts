import "./assets/main.css";

import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";

// Import stores for initialization
import { useAuthStore } from "./stores/auth";
import { useCartStore } from "./stores/cart";

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router);

// Initialize stores
const authStore = useAuthStore();
const cartStore = useCartStore();

// Initialize auth and cart
authStore.initialize();
cartStore.initialize();

app.mount("#app");
