<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h2 class="text-3xl font-serif font-bold text-gray-900">
          Welcome Back
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Sign in to your account to continue shopping
        </p>
      </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <div class="mt-1">
              <input
                id="email"
                v-model="form.email"
                type="email"
                autocomplete="email"
                required
                class="input-field"
                :class="{ 'border-red-500': errors.email }"
              />
              <p v-if="errors.email" class="mt-1 text-sm text-red-600">
                {{ errors.email }}
              </p>
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              Password
            </label>
            <div class="mt-1">
              <input
                id="password"
                v-model="form.password"
                type="password"
                autocomplete="current-password"
                required
                class="input-field"
                :class="{ 'border-red-500': errors.password }"
              />
              <p v-if="errors.password" class="mt-1 text-sm text-red-600">
                {{ errors.password }}
              </p>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="form.rememberMe"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                Remember me
              </label>
            </div>

            <div class="text-sm">
              <button
                type="button"
                @click="showForgotPassword = true"
                class="font-medium text-primary-600 hover:text-primary-500"
              >
                Forgot your password?
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading"
              class="w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading">Signing in...</span>
              <span v-else>Sign in</span>
            </button>
          </div>

          <div v-if="error" class="rounded-md bg-red-50 p-4">
            <div class="text-sm text-red-700">
              {{ error }}
            </div>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Don't have an account?</span>
            </div>
          </div>

          <div class="mt-6">
            <router-link
              to="/register"
              class="w-full btn-outline text-center block py-3"
            >
              Create new account
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Forgot Password Modal -->
    <div v-if="showForgotPassword" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Reset Password</h3>
          <form @submit.prevent="handleForgotPassword">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Email address
              </label>
              <input
                v-model="forgotPasswordEmail"
                type="email"
                required
                class="input-field"
                placeholder="Enter your email"
              />
            </div>
            <div class="flex space-x-3">
              <button
                type="submit"
                :disabled="forgotPasswordLoading"
                class="flex-1 btn-primary py-2 disabled:opacity-50"
              >
                <span v-if="forgotPasswordLoading">Sending...</span>
                <span v-else>Send Reset Link</span>
              </button>
              <button
                type="button"
                @click="showForgotPassword = false"
                class="flex-1 btn-outline py-2"
              >
                Cancel
              </button>
            </div>
          </form>
          <div v-if="forgotPasswordMessage" class="mt-4 text-sm text-green-600">
            {{ forgotPasswordMessage }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loading = ref(false)
const error = ref('')
const showForgotPassword = ref(false)
const forgotPasswordEmail = ref('')
const forgotPasswordLoading = ref(false)
const forgotPasswordMessage = ref('')

const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

const errors = reactive({
  email: '',
  password: ''
})

const validateForm = () => {
  errors.email = ''
  errors.password = ''

  if (!form.email) {
    errors.email = 'Email is required'
    return false
  }

  if (!form.password) {
    errors.password = 'Password is required'
    return false
  }

  if (form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
    return false
  }

  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    error.value = ''

    const { error: signInError } = await authStore.signIn(form.email, form.password)

    if (signInError) {
      error.value = signInError.message
      return
    }

    // Redirect to intended page or home
    const redirectTo = route.query.redirect as string || '/'
    router.push(redirectTo)
  } catch (err) {
    error.value = 'An unexpected error occurred'
    console.error('Login error:', err)
  } finally {
    loading.value = false
  }
}

const handleForgotPassword = async () => {
  try {
    forgotPasswordLoading.value = true
    forgotPasswordMessage.value = ''

    const { error: resetError } = await authStore.resetPassword(forgotPasswordEmail.value)

    if (resetError) {
      error.value = resetError.message
      return
    }

    forgotPasswordMessage.value = 'Password reset link sent to your email'
    setTimeout(() => {
      showForgotPassword.value = false
      forgotPasswordEmail.value = ''
      forgotPasswordMessage.value = ''
    }, 3000)
  } catch (err) {
    error.value = 'Failed to send reset email'
    console.error('Password reset error:', err)
  } finally {
    forgotPasswordLoading.value = false
  }
}
</script>
