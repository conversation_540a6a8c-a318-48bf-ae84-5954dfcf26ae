<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-serif font-bold text-gray-900 mb-8">Shopping Cart</h1>

      <!-- Empty Cart -->
      <div v-if="isEmpty" class="text-center py-12">
        <div class="text-6xl mb-4">🛒</div>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your cart is empty</h2>
        <p class="text-gray-600 mb-8">
          Discover our authentic Tunisian products and add them to your cart
        </p>
        <router-link to="/products" class="btn-primary text-lg px-8 py-3">
          Start Shopping
        </router-link>
      </div>

      <!-- Cart Items -->
      <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Items List -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-6">
                Cart Items ({{ itemCount }})
              </h2>
              
              <div class="space-y-6">
                <div 
                  v-for="item in items" 
                  :key="item.product.id"
                  class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"
                >
                  <!-- Product Image -->
                  <div class="flex-shrink-0">
                    <img 
                      :src="item.product.image_url || '/placeholder-product.jpg'" 
                      :alt="item.product.name"
                      class="w-20 h-20 object-cover rounded-lg"
                    />
                  </div>

                  <!-- Product Info -->
                  <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-medium text-gray-900 truncate">
                      {{ item.product.name }}
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">
                      ${{ item.product.price.toFixed(2) }} each
                    </p>
                    <p class="text-sm text-gray-500 mt-1">
                      {{ item.product.stock_quantity }} in stock
                    </p>
                  </div>

                  <!-- Quantity Controls -->
                  <div class="flex items-center space-x-2">
                    <button
                      @click="updateQuantity(item.product.id, item.quantity - 1)"
                      :disabled="item.quantity <= 1"
                      class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                      </svg>
                    </button>
                    
                    <span class="w-12 text-center font-medium">{{ item.quantity }}</span>
                    
                    <button
                      @click="updateQuantity(item.product.id, item.quantity + 1)"
                      :disabled="item.quantity >= item.product.stock_quantity"
                      class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </button>
                  </div>

                  <!-- Item Total -->
                  <div class="text-right">
                    <p class="text-lg font-semibold text-gray-900">
                      ${{ (item.product.price * item.quantity).toFixed(2) }}
                    </p>
                    <button
                      @click="removeItem(item.product.id)"
                      class="text-sm text-red-600 hover:text-red-800 mt-1"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-6 sticky top-24">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Order Summary</h2>
            
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-gray-600">Subtotal</span>
                <span class="font-medium">${{ totalAmount.toFixed(2) }}</span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-gray-600">Shipping</span>
                <span class="font-medium">Free</span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-gray-600">Tax</span>
                <span class="font-medium">Calculated at checkout</span>
              </div>
              
              <div class="border-t border-gray-200 pt-4">
                <div class="flex justify-between">
                  <span class="text-lg font-semibold text-gray-900">Total</span>
                  <span class="text-lg font-semibold text-gray-900">
                    ${{ totalAmount.toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>

            <div class="mt-6 space-y-3">
              <router-link 
                to="/checkout" 
                class="w-full btn-primary text-center block py-3"
              >
                Proceed to Checkout
              </router-link>
              
              <router-link 
                to="/products" 
                class="w-full btn-outline text-center block py-3"
              >
                Continue Shopping
              </router-link>
            </div>

            <button
              @click="clearCart"
              class="w-full mt-4 text-sm text-red-600 hover:text-red-800"
            >
              Clear Cart
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCartStore } from '@/stores/cart'

const cartStore = useCartStore()

const items = computed(() => cartStore.items)
const itemCount = computed(() => cartStore.itemCount)
const totalAmount = computed(() => cartStore.totalAmount)
const isEmpty = computed(() => cartStore.isEmpty)

const updateQuantity = (productId: string, quantity: number) => {
  cartStore.updateQuantity(productId, quantity)
}

const removeItem = (productId: string) => {
  cartStore.removeItem(productId)
}

const clearCart = () => {
  if (confirm('Are you sure you want to clear your cart?')) {
    cartStore.clearCart()
  }
}
</script>
